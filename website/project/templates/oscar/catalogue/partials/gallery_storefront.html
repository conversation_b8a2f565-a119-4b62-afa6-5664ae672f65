{% load image_tags %}
{% load i18n %}
{% load custom_purchase_info_tags %}
{% load static %}

{% purchase_info_for_product request product as session %}
{% with all_images=product.images.all %}

<!-- CRITICAL: Server-side rendered gallery for optimal LCP -->
<div class="product-gallery-critical">
    {% if all_images|length > 1 %}
        <!-- Main Image Display - Server-side rendered for LCP -->
        <div class="relative bg-gray-50 rounded-lg overflow-hidden mb-4" id="main-gallery-container">
            <!-- CRITICAL: Static first image for immediate LCP -->
            {% with first_image=all_images.0 %}
                {% oscar_thumbnail first_image.original "320x240" upscale=False crop="center" quality=80 as thumb_mobile %}
                {% oscar_thumbnail first_image.original "640x480" upscale=False crop="center" quality=85 as thumb_mobile_2x %}
                {% oscar_thumbnail first_image.original "800x600" upscale=False crop="center" quality=95 as thumb_large %}
                <img id="main-gallery-image"
                     src="{{ thumb_mobile.url }}"
                     srcset="{{ thumb_mobile.url }} 320w, {{ thumb_mobile_2x.url }} 640w, {{ thumb_large.url }} 800w"
                     sizes="(max-width: 480px) 280px, (max-width: 768px) 400px, 600px"
                     alt="{{ product.get_title }} - {% trans 'Image' %} 1"
                     fetchpriority="high"
                     loading="eager"
                     decoding="async"
                     width="320"
                     height="240"
                     class="w-full aspect-[4/3] object-contain cursor-pointer"
                     onclick="openGalleryModal(0)">
            {% endwith %}

            <!-- Navigation Arrows - Hidden initially, shown after JS loads -->
            <button id="prev-btn"
                    onclick="previousGalleryImage()"
                    aria-label="{% trans 'Previous image' %}"
                    class="absolute left-2 top-1/2 -translate-y-1/2 bg-white bg-opacity-80 hover:bg-opacity-100 rounded-full p-2 shadow-lg transition-opacity"
                    style="display: none;">
                <svg class="w-5 h-5 text-gray-600" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                    <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                </svg>
            </button>
            <button id="next-btn"
                    onclick="nextGalleryImage()"
                    aria-label="{% trans 'Next image' %}"
                    class="absolute right-2 top-1/2 -translate-y-1/2 bg-white bg-opacity-80 hover:bg-opacity-100 rounded-full p-2 shadow-lg transition-opacity"
                    style="display: none;">
                <svg class="w-5 h-5 text-gray-600" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                </svg>
            </button>
        </div>

        <!-- Thumbnail Navigation - Lazy loaded after main image -->
        <div class="flex space-x-2 overflow-x-auto p-1.5" id="thumbnail-container">
            {% for image in all_images %}
                {% oscar_thumbnail image.original "65x55" crop="center" as thumb_small %}
                <button onclick="setGalleryImage({{ forloop.counter0 }})"
                        aria-label="{% trans 'View image' %} {{ forloop.counter }}"
                        class="flex-shrink-0 w-16 h-16 bg-gray-100 rounded-md overflow-hidden hover:ring-2 hover:ring-gray-300 transition-all duration-200 gallery-thumb"
                        data-index="{{ forloop.counter0 }}"
                        {% if forloop.first %}data-active="true"{% endif %}>
                    <img src="{{ thumb_small.url }}"
                         alt="{{ product.get_title }} - {% trans 'Image' %} {{ forloop.counter }}"
                         loading="{% if forloop.counter <= 4 %}eager{% else %}lazy{% endif %}"
                         class="w-full h-full object-cover">
                </button>
            {% endfor %}
        </div>
        
    {% else %}
        <!-- Single Image Display -->
        <div class="relative bg-gray-50 rounded-lg overflow-hidden">
            {% with image=product.primary_image %}
                {% oscar_thumbnail image.original "320x240" upscale=False crop="center" quality=80 as thumb_mobile %}
                {% oscar_thumbnail image.original "640x480" upscale=False crop="center" quality=85 as thumb_mobile_2x %}
                {% oscar_thumbnail image.original "800x600" upscale=False crop="center" quality=95 as thumb_large %}
                <img id="main-gallery-image"
                     src="{{ thumb_mobile.url }}"
                     srcset="{{ thumb_mobile.url }} 320w, {{ thumb_mobile_2x.url }} 640w, {{ thumb_large.url }} 800w"
                     sizes="(max-width: 480px) 280px, (max-width: 768px) 400px, 600px"
                     alt='{{ product.get_title }}'
                     fetchpriority="high"
                     loading="eager"
                     decoding="async"
                     width="320"
                     height="240"
                     class="w-full aspect-[4/3] object-contain cursor-pointer"
                     onclick="openGalleryModal(0)">
            {% endwith %}
        </div>
    {% endif %}
</div>
<!-- Modal for Full Size Images - Lazy loaded -->
<div id="gallery-modal"
     class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75 backdrop-blur-sm"
     style="display: none;"
     onclick="closeGalleryModal(event)">

    <!-- Modal Navigation Buttons -->
    <button id="modal-prev-btn"
            onclick="previousModalImage(event)"
            aria-label="{% trans 'Previous image' %}"
            class="fixed left-4 top-1/2 -translate-y-1/2 bg-white bg-opacity-80 hover:bg-opacity-100 rounded-full p-3 shadow-lg z-10"
            style="display: none;">
        <svg class="w-6 h-6 text-gray-600" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
            <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd"></path>
        </svg>
    </button>
    <button id="modal-next-btn"
            onclick="nextModalImage(event)"
            aria-label="{% trans 'Next image' %}"
            class="fixed right-4 top-1/2 -translate-y-1/2 bg-white bg-opacity-80 hover:bg-opacity-100 rounded-full p-3 shadow-lg z-10"
            style="display: none;">
        <svg class="w-6 h-6 text-gray-600" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
        </svg>
    </button>

    <!-- Close Button -->
    <button onclick="closeGalleryModal(event)"
            aria-label="{% trans 'Close image gallery' %}"
            class="fixed top-4 right-4 bg-white bg-opacity-80 hover:bg-opacity-100 rounded-full p-2 shadow-lg z-10">
        <svg class="w-6 h-6 text-gray-600" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
        </svg>
    </button>

    <!-- Image Container -->
    <div class="relative max-w-4xl max-h-full min-h-96 p-4 flex items-center justify-center" onclick="event.stopPropagation()">
        <!-- Loading indicator -->
        <div id="modal-loading" class="flex items-center justify-center" style="display: none;">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-white"></div>
        </div>

        <!-- Original image -->
        <img id="modal-image"
             class="max-w-full max-h-full object-contain"
             style="display: none;">
    </div>
</div>

<!-- Optimized Gallery JavaScript - Loads after LCP -->
<script>
// CRITICAL: Gallery data and functions - minimal blocking JavaScript
let galleryData = [
    {% for image in all_images %}
    {
        originalUrl: '{{ image.original.url }}',
        alt: '{{ product.get_title }} - {% trans "Image" %} {{ forloop.counter }}',
        imageId: {{ image.id }},
        {% if forloop.first %}
        thumbnails: {
            mobile: '{% oscar_thumbnail image.original "320x240" upscale=False crop="center" quality=80 as thumb_mobile %}{{ thumb_mobile.url }}',
            mobile2x: '{% oscar_thumbnail image.original "640x480" upscale=False crop="center" quality=85 as thumb_mobile_2x %}{{ thumb_mobile_2x.url }}',
            large: '{% oscar_thumbnail image.original "800x600" upscale=False crop="center" quality=95 as thumb_large %}{{ thumb_large.url }}'
        }
        {% else %}
        thumbnails: null
        {% endif %}
    }{% if not forloop.last %},{% endif %}
    {% endfor %}
];

let currentGalleryIndex = 0;
let modalIndex = 0;
let isModalOpen = false;

// Initialize gallery after DOM is ready but don't block LCP
function initGallery() {
    // Show navigation buttons if multiple images
    if (galleryData.length > 1) {
        document.getElementById('prev-btn').style.display = 'block';
        document.getElementById('next-btn').style.display = 'block';
        document.getElementById('modal-prev-btn').style.display = 'block';
        document.getElementById('modal-next-btn').style.display = 'block';
    }

    // Update thumbnail active states
    updateThumbnailStates();

    // Preload next few images in background
    preloadNextImages();
}

function updateThumbnailStates() {
    document.querySelectorAll('.gallery-thumb').forEach((thumb, index) => {
        if (index === currentGalleryIndex) {
            thumb.classList.add('ring-2', 'ring-blue-500');
            thumb.setAttribute('data-active', 'true');
        } else {
            thumb.classList.remove('ring-2', 'ring-blue-500');
            thumb.removeAttribute('data-active');
        }
    });
}
function preloadNextImages() {
    // Preload next 2-3 images in background for smooth navigation
    for (let i = 1; i <= Math.min(3, galleryData.length - 1); i++) {
        const nextIndex = (currentGalleryIndex + i) % galleryData.length;
        const imageData = galleryData[nextIndex];
        if (!imageData.thumbnails) {
            // Use original URL as fallback
            imageData.thumbnails = {
                mobile: imageData.originalUrl,
                mobile2x: imageData.originalUrl,
                large: imageData.originalUrl
            };
        }

        // Preload the image
        const img = new Image();
        img.src = imageData.thumbnails.mobile2x || imageData.thumbnails.mobile;
    }
}

function setGalleryImage(index) {
    if (currentGalleryIndex === index) return;

    currentGalleryIndex = index;
    const imageData = galleryData[index];
    const mainImage = document.getElementById('main-gallery-image');

    if (!imageData.thumbnails) {
        // Use original URL as fallback
        imageData.thumbnails = {
            mobile: imageData.originalUrl,
            mobile2x: imageData.originalUrl,
            large: imageData.originalUrl
        };
    }

    // Update main image
    mainImage.src = imageData.thumbnails.mobile;
    mainImage.srcset = `${imageData.thumbnails.mobile} 320w, ${imageData.thumbnails.mobile2x} 640w, ${imageData.thumbnails.large} 800w`;
    mainImage.alt = imageData.alt;

    // Update thumbnail states
    updateThumbnailStates();
}

function nextGalleryImage() {
    const newIndex = (currentGalleryIndex + 1) % galleryData.length;
    setGalleryImage(newIndex);
}

function previousGalleryImage() {
    const newIndex = currentGalleryIndex === 0 ? galleryData.length - 1 : currentGalleryIndex - 1;
    setGalleryImage(newIndex);
}
// Modal functionality
function openGalleryModal(index) {
    modalIndex = index;
    isModalOpen = true;
    const modal = document.getElementById('gallery-modal');
    const modalImage = document.getElementById('modal-image');
    const modalLoading = document.getElementById('modal-loading');

    modal.style.display = 'flex';
    document.body.style.overflow = 'hidden';

    loadModalImage(index);
}

function closeGalleryModal(event) {
    if (event && event.target !== event.currentTarget) return;

    isModalOpen = false;
    const modal = document.getElementById('gallery-modal');
    modal.style.display = 'none';
    document.body.style.overflow = '';
}

function nextModalImage(event) {
    if (event) event.stopPropagation();
    const newIndex = (modalIndex + 1) % galleryData.length;
    modalIndex = newIndex;
    loadModalImage(newIndex);
}

function previousModalImage(event) {
    if (event) event.stopPropagation();
    const newIndex = modalIndex === 0 ? galleryData.length - 1 : modalIndex - 1;
    modalIndex = newIndex;
    loadModalImage(newIndex);
}

function loadModalImage(index) {
    const imageUrl = galleryData[index].originalUrl;
    const modalImage = document.getElementById('modal-image');
    const modalLoading = document.getElementById('modal-loading');

    modalLoading.style.display = 'flex';
    modalImage.style.display = 'none';

    const img = new Image();
    img.onload = () => {
        modalImage.src = imageUrl;
        modalImage.alt = galleryData[index].alt;
        modalImage.style.display = 'block';
        modalLoading.style.display = 'none';
    };
    img.onerror = () => {
        console.error('Failed to load original image:', imageUrl);
        modalImage.src = imageUrl;
        modalImage.alt = galleryData[index].alt;
        modalImage.style.display = 'block';
        modalLoading.style.display = 'none';
    };
    img.src = imageUrl;
}

// Keyboard navigation
document.addEventListener('keydown', function(event) {
    if (isModalOpen) {
        if (event.key === 'Escape') {
            closeGalleryModal();
        } else if (event.key === 'ArrowRight' || event.key === 'd') {
            nextModalImage();
        } else if (event.key === 'ArrowLeft' || event.key === 'a') {
            previousModalImage();
        } else if (event.key === ' ') {
            event.preventDefault();
            nextModalImage();
        }
    }
});

// Initialize gallery after page load to avoid blocking LCP
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initGallery);
} else {
    // Use setTimeout to ensure this runs after LCP
    setTimeout(initGallery, 100);
}
</script>

{% endwith %}
